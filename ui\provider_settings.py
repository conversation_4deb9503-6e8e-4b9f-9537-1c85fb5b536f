"""
Provider Settings UI for BlendPro v2.1.0
User interface for AI provider registration, testing, and configuration
"""

import bpy
from bpy.types import Panel, Operator, PropertyGroup
from bpy.props import StringProperty, BoolProperty, EnumProperty, FloatProperty, IntProperty
import json
from typing import List, Tuple, Optional

from ..config.providers import AIProvider, ProviderType, AuthenticationType, get_default_providers
from ..config.agent_configs import AgentType, get_default_agent_configurations
from ..utils.provider_manager import get_provider_manager
from ..utils.dynamic_api_client import get_dynamic_api_client
from ..utils.logger import get_logger

class BLENDPRO_PG_ProviderSettings(PropertyGroup):
    """Property group for provider settings"""
    
    # Provider basic info
    provider_name: StringProperty(
        name="Provider Name",
        description="Unique name for the provider",
        default=""
    )
    
    display_name: StringProperty(
        name="Display Name",
        description="Human-readable name for the provider",
        default=""
    )
    
    provider_type: EnumProperty(
        name="Provider Type",
        description="Type of AI provider",
        items=[
            ('openai', 'OpenAI', 'OpenAI API'),
            ('anthropic', 'Anthropic', 'Anthropic Claude API'),
            ('google', 'Google', 'Google AI API'),
            ('mistral', 'Mistral', 'Mistral AI API'),
            ('openrouter', 'OpenRouter', 'OpenRouter API'),
            ('custom', 'Custom', 'Custom API endpoint')
        ],
        default='openai'
    )
    
    api_endpoint: StringProperty(
        name="API Endpoint",
        description="API endpoint URL",
        default="https://api.openai.com/v1"
    )
    
    api_key: StringProperty(
        name="API Key",
        description="API key for authentication",
        default="",
        subtype='PASSWORD'
    )
    
    # Authentication settings
    auth_type: EnumProperty(
        name="Authentication Type",
        description="Type of authentication",
        items=[
            ('api_key', 'API Key', 'API Key authentication'),
            ('bearer', 'Bearer Token', 'Bearer token authentication'),
            ('oauth', 'OAuth', 'OAuth authentication'),
            ('custom_header', 'Custom Header', 'Custom header authentication')
        ],
        default='api_key'
    )
    
    # Capabilities
    supports_vision: BoolProperty(
        name="Supports Vision",
        description="Provider supports vision/image analysis",
        default=False
    )
    
    supports_function_calling: BoolProperty(
        name="Supports Function Calling",
        description="Provider supports function calling",
        default=False
    )
    
    supports_streaming: BoolProperty(
        name="Supports Streaming",
        description="Provider supports streaming responses",
        default=True
    )
    
    # Rate limiting
    requests_per_minute: IntProperty(
        name="Requests per Minute",
        description="Maximum requests per minute",
        default=60,
        min=1,
        max=10000
    )
    
    tokens_per_minute: IntProperty(
        name="Tokens per Minute",
        description="Maximum tokens per minute",
        default=100000,
        min=1000,
        max=10000000
    )
    
    concurrent_requests: IntProperty(
        name="Concurrent Requests",
        description="Maximum concurrent requests",
        default=10,
        min=1,
        max=100
    )
    
    # Cost configuration
    input_cost_per_1k_tokens: FloatProperty(
        name="Input Cost per 1K Tokens",
        description="Cost per 1000 input tokens (USD)",
        default=0.0,
        min=0.0,
        precision=6
    )
    
    output_cost_per_1k_tokens: FloatProperty(
        name="Output Cost per 1K Tokens",
        description="Cost per 1000 output tokens (USD)",
        default=0.0,
        min=0.0,
        precision=6
    )
    
    image_cost_per_request: FloatProperty(
        name="Image Cost per Request",
        description="Cost per image analysis request (USD)",
        default=0.0,
        min=0.0,
        precision=6
    )
    
    # Configuration
    default_model: StringProperty(
        name="Default Model",
        description="Default model for this provider",
        default=""
    )
    
    timeout: FloatProperty(
        name="Timeout",
        description="Request timeout in seconds",
        default=30.0,
        min=1.0,
        max=300.0
    )
    
    max_retries: IntProperty(
        name="Max Retries",
        description="Maximum number of retries",
        default=3,
        min=0,
        max=10
    )
    
    is_active: BoolProperty(
        name="Active",
        description="Provider is active and available",
        default=True
    )
    
    # Status
    test_status: EnumProperty(
        name="Test Status",
        description="Last test result",
        items=[
            ('unknown', 'Unknown', 'Not tested'),
            ('success', 'Success', 'Test successful'),
            ('failed', 'Failed', 'Test failed')
        ],
        default='unknown'
    )
    
    last_tested: StringProperty(
        name="Last Tested",
        description="Last test timestamp",
        default=""
    )

class BLENDPRO_OT_AddProvider(Operator):
    """Add new AI provider"""
    bl_idname = "blendpro.add_provider"
    bl_label = "Add Provider"
    bl_description = "Add a new AI provider"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        try:
            settings = context.scene.blendpro_provider_settings
            provider_manager = get_provider_manager()
            
            # Create provider from settings
            provider = AIProvider(
                name=settings.provider_name,
                display_name=settings.display_name,
                provider_type=ProviderType(settings.provider_type),
                api_endpoint=settings.api_endpoint,
                api_key=settings.api_key,
                authentication_type=AuthenticationType(settings.auth_type),
                supports_vision=settings.supports_vision,
                supports_function_calling=settings.supports_function_calling,
                supports_streaming=settings.supports_streaming,
                default_model=settings.default_model,
                timeout=settings.timeout,
                max_retries=settings.max_retries,
                is_active=settings.is_active
            )
            
            # Set rate limits
            provider.rate_limits.requests_per_minute = settings.requests_per_minute
            provider.rate_limits.tokens_per_minute = settings.tokens_per_minute
            provider.rate_limits.concurrent_requests = settings.concurrent_requests
            
            # Set cost config
            provider.cost_config.input_cost_per_1k_tokens = settings.input_cost_per_1k_tokens
            provider.cost_config.output_cost_per_1k_tokens = settings.output_cost_per_1k_tokens
            provider.cost_config.image_cost_per_request = settings.image_cost_per_request
            
            # Register provider
            success, errors = provider_manager.register_provider(provider)
            
            if success:
                self.report({'INFO'}, f"Provider '{settings.provider_name}' added successfully")
                # Clear form
                self._clear_form(settings)
            else:
                error_msg = "; ".join(errors)
                self.report({'ERROR'}, f"Failed to add provider: {error_msg}")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error adding provider: {str(e)}")
            return {'CANCELLED'}
    
    def _clear_form(self, settings):
        """Clear the provider form"""
        settings.provider_name = ""
        settings.display_name = ""
        settings.api_endpoint = "https://api.openai.com/v1"
        settings.api_key = ""
        settings.default_model = ""

class BLENDPRO_OT_TestProvider(Operator):
    """Test AI provider connection"""
    bl_idname = "blendpro.test_provider"
    bl_label = "Test Provider"
    bl_description = "Test connection to AI provider"
    bl_options = {'REGISTER'}
    
    provider_name: StringProperty(name="Provider Name")
    
    def execute(self, context):
        try:
            dynamic_client = get_dynamic_api_client()
            result = dynamic_client.test_provider_connection(self.provider_name)
            
            if result["success"]:
                self.report({'INFO'}, f"Provider '{self.provider_name}' test successful")
                self.report({'INFO'}, f"Response: {result.get('content', 'N/A')}")
                self.report({'INFO'}, f"Model: {result.get('model', 'N/A')}")
                self.report({'INFO'}, f"Response time: {result.get('response_time', 0):.2f}s")
            else:
                self.report({'ERROR'}, f"Provider '{self.provider_name}' test failed: {result.get('error', 'Unknown error')}")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error testing provider: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_RemoveProvider(Operator):
    """Remove AI provider"""
    bl_idname = "blendpro.remove_provider"
    bl_label = "Remove Provider"
    bl_description = "Remove AI provider"
    bl_options = {'REGISTER', 'UNDO'}

    provider_name: StringProperty(name="Provider Name")

    def execute(self, context):
        try:
            provider_manager = get_provider_manager()
            success = provider_manager.remove_provider(self.provider_name)

            if success:
                self.report({'INFO'}, f"Provider '{self.provider_name}' removed successfully")
            else:
                self.report({'ERROR'}, f"Failed to remove provider '{self.provider_name}' - may be in use by agents")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error removing provider: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_LoadDefaultProviders(Operator):
    """Load default providers"""
    bl_idname = "blendpro.load_default_providers"
    bl_label = "Load Default Providers"
    bl_description = "Load default AI providers (OpenAI, Anthropic, OpenRouter)"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            provider_manager = get_provider_manager()
            default_providers = get_default_providers()

            added_count = 0
            for provider_name, provider in default_providers.items():
                if not provider_manager.get_provider(provider_name):
                    success, errors = provider_manager.register_provider(provider)
                    if success:
                        added_count += 1

            self.report({'INFO'}, f"Loaded {added_count} default providers")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error loading default providers: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_PT_ProviderSettings(Panel):
    """Provider Settings Panel - Redirects to addon preferences"""
    bl_label = "AI Provider Settings"
    bl_idname = "BLENDPRO_PT_provider_settings"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "BlendPro"
    bl_parent_id = "BLENDPRO_PT_main_panel"

    def draw(self, context):
        layout = self.layout

        # Info message
        box = layout.box()
        box.label(text="Provider settings have been moved to", icon='INFO')
        box.label(text="Addon Preferences for better organization.")

        # Direct link to addon preferences
        prefs_row = layout.row()
        prefs_row.scale_y = 1.3
        prefs_row.operator("screen.userpref_show", text="Open Provider Settings", icon='PREFERENCES')

        try:
            provider_manager = get_provider_manager()
            providers = provider_manager.get_all_providers()

            if providers:
                for provider_name, provider in providers.items():
                    row = box.row()

                    # Provider info
                    col = row.column()
                    col.label(text=f"{provider.display_name} ({provider_name})")
                    col.label(text=f"Type: {provider.provider_type.value}, Status: {provider.test_status}")

                    # Actions
                    col = row.column()
                    test_op = col.operator("blendpro.test_provider", text="Test", icon='PLAY')
                    test_op.provider_name = provider_name

                    remove_op = col.operator("blendpro.remove_provider", text="Remove", icon='X')
                    remove_op.provider_name = provider_name
            else:
                box.label(text="No providers registered")
                box.operator("blendpro.load_default_providers", text="Load Default Providers", icon='IMPORT')

        except Exception as e:
            box.label(text=f"Error loading providers: {str(e)}", icon='ERROR')

        layout.separator()

        # Add new provider section
        box = layout.box()
        box.label(text="Add New Provider", icon='ADD')

        # Basic info
        col = box.column()
        col.prop(settings, "provider_name")
        col.prop(settings, "display_name")
        col.prop(settings, "provider_type")
        col.prop(settings, "api_endpoint")
        col.prop(settings, "api_key")

        # Authentication
        col.separator()
        col.label(text="Authentication", icon='LOCKED')
        col.prop(settings, "auth_type")

        # Capabilities
        col.separator()
        col.label(text="Capabilities", icon='SETTINGS')
        row = col.row()
        row.prop(settings, "supports_vision")
        row.prop(settings, "supports_function_calling")
        row.prop(settings, "supports_streaming")

        # Rate limiting
        col.separator()
        col.label(text="Rate Limiting", icon='TIME')
        col.prop(settings, "requests_per_minute")
        col.prop(settings, "tokens_per_minute")
        col.prop(settings, "concurrent_requests")

        # Cost configuration
        col.separator()
        col.label(text="Cost Configuration", icon='FUND')
        col.prop(settings, "input_cost_per_1k_tokens")
        col.prop(settings, "output_cost_per_1k_tokens")
        col.prop(settings, "image_cost_per_request")

        # Configuration
        col.separator()
        col.label(text="Configuration", icon='PREFERENCES')
        col.prop(settings, "default_model")
        col.prop(settings, "timeout")
        col.prop(settings, "max_retries")
        col.prop(settings, "is_active")

        # Add button
        col.separator()
        col.operator("blendpro.add_provider", text="Add Provider", icon='ADD')

def register():
    """Register UI classes"""
    bpy.utils.register_class(BLENDPRO_PG_ProviderSettings)
    bpy.utils.register_class(BLENDPRO_OT_AddProvider)
    bpy.utils.register_class(BLENDPRO_OT_TestProvider)
    bpy.utils.register_class(BLENDPRO_OT_RemoveProvider)
    bpy.utils.register_class(BLENDPRO_OT_LoadDefaultProviders)
    bpy.utils.register_class(BLENDPRO_PT_ProviderSettings)

    # Register property group
    bpy.types.Scene.blendpro_provider_settings = bpy.props.PointerProperty(type=BLENDPRO_PG_ProviderSettings)

def unregister():
    """Unregister UI classes"""
    bpy.utils.unregister_class(BLENDPRO_PT_ProviderSettings)
    bpy.utils.unregister_class(BLENDPRO_OT_LoadDefaultProviders)
    bpy.utils.unregister_class(BLENDPRO_OT_RemoveProvider)
    bpy.utils.unregister_class(BLENDPRO_OT_TestProvider)
    bpy.utils.unregister_class(BLENDPRO_OT_AddProvider)
    bpy.utils.unregister_class(BLENDPRO_PG_ProviderSettings)

    # Unregister property group
    del bpy.types.Scene.blendpro_provider_settings
