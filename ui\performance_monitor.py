"""
Performance Monitor for BlendPro v2.1.0
User interface for tracking system metrics and performance
"""

import bpy
from bpy.types import Panel, Operator, PropertyGroup
from bpy.props import StringProperty, BoolProperty, EnumProperty, FloatProperty, IntProperty
import time
from typing import Dict, List, Any, Optional

from ..utils.provider_manager import get_provider_manager
from ..utils.dynamic_api_client import get_dynamic_api_client
from ..core.agent_orchestrator import get_agent_orchestrator
from ..vision.multi_modal_vision import get_multi_modal_vision
from ..utils.logger import get_logger

class BLENDPRO_PG_PerformanceSettings(PropertyGroup):
    """Property group for performance monitoring settings"""
    
    monitor_providers: Bool<PERSON>roperty(
        name="Monitor Providers",
        description="Monitor AI provider performance",
        default=True
    )
    
    monitor_agents: BoolProperty(
        name="Monitor Agents",
        description="Monitor agent performance",
        default=True
    )
    
    monitor_vision: BoolProperty(
        name="Monitor Vision",
        description="Monitor vision system performance",
        default=True
    )
    
    monitor_memory: BoolProperty(
        name="Monitor Memory",
        description="Monitor memory usage",
        default=False
    )
    
    auto_refresh: <PERSON><PERSON><PERSON><PERSON><PERSON>(
        name="Auto Refresh",
        description="Automatically refresh performance data",
        default=False
    )
    
    refresh_interval: FloatProperty(
        name="Refresh Interval",
        description="Auto refresh interval in seconds",
        default=5.0,
        min=1.0,
        max=60.0
    )
    
    show_detailed_metrics: BoolProperty(
        name="Show Detailed Metrics",
        description="Show detailed performance metrics",
        default=False
    )

class BLENDPRO_OT_RefreshPerformance(Operator):
    """Refresh performance data"""
    bl_idname = "blendpro.refresh_performance"
    bl_label = "Refresh Performance"
    bl_description = "Refresh performance monitoring data"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            # Store refresh timestamp
            context.scene.blendpro_performance_last_refresh = str(time.time())
            
            self.report({'INFO'}, "Performance data refreshed")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error refreshing performance data: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_ClearPerformanceData(Operator):
    """Clear performance data"""
    bl_idname = "blendpro.clear_performance_data"
    bl_label = "Clear Performance Data"
    bl_description = "Clear all performance monitoring data"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        try:
            # Clear agent performance data
            agent_orchestrator = get_agent_orchestrator()
            agent_orchestrator._agent_performance = {
                agent_type: {"success_rate": 1.0, "avg_response_time": 0.0, "total_requests": 0}
                for agent_type in agent_orchestrator._agent_performance.keys()
            }
            
            # Clear vision caches
            multi_modal_vision = get_multi_modal_vision()
            multi_modal_vision.clear_all_vision_caches()
            
            self.report({'INFO'}, "Performance data cleared")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error clearing performance data: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_ExportPerformanceReport(Operator):
    """Export performance report"""
    bl_idname = "blendpro.export_performance_report"
    bl_label = "Export Report"
    bl_description = "Export performance report to file"
    bl_options = {'REGISTER'}
    
    filepath: StringProperty(
        name="File Path",
        description="Path to export file",
        default="performance_report.json",
        subtype='FILE_PATH'
    )
    
    def execute(self, context):
        try:
            import json
            
            # Collect performance data
            report_data = self._collect_performance_data()
            
            # Export to file
            with open(self.filepath, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
            
            self.report({'INFO'}, f"Performance report exported to {self.filepath}")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Export failed: {str(e)}")
            return {'CANCELLED'}
    
    def _collect_performance_data(self) -> Dict[str, Any]:
        """Collect all performance data"""
        data = {
            "timestamp": time.time(),
            "providers": {},
            "agents": {},
            "vision": {},
            "system": {}
        }
        
        try:
            # Provider data
            provider_manager = get_provider_manager()
            data["providers"] = provider_manager.get_provider_status_summary()
            
            # Agent data
            agent_orchestrator = get_agent_orchestrator()
            data["agents"] = agent_orchestrator.get_agent_performance_summary()
            
            # Vision data
            multi_modal_vision = get_multi_modal_vision()
            data["vision"] = multi_modal_vision.get_vision_performance_summary()
            
        except Exception as e:
            data["error"] = str(e)
        
        return data
    
    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class BLENDPRO_PT_PerformanceMonitor(Panel):
    """Performance Monitor Panel - Redirects to addon preferences"""
    bl_label = "Performance Monitor"
    bl_idname = "BLENDPRO_PT_performance_monitor"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "BlendPro"
    bl_parent_id = "BLENDPRO_PT_main_panel"

    def draw(self, context):
        layout = self.layout

        # Info message
        box = layout.box()
        box.label(text="Performance monitoring settings", icon='INFO')
        box.label(text="have been moved to Addon Preferences.")

        # Direct link to addon preferences
        prefs_row = layout.row()
        prefs_row.scale_y = 1.3
        prefs_row.operator("screen.userpref_show", text="Open Performance Settings", icon='PREFERENCES')
            col.prop(settings, "refresh_interval")
        
        layout.separator()
        
        # Monitoring options
        box = layout.box()
        box.label(text="Monitoring Options", icon='SETTINGS')
        
        col = box.column()
        col.prop(settings, "monitor_providers")
        col.prop(settings, "monitor_agents")
        col.prop(settings, "monitor_vision")
        col.prop(settings, "monitor_memory")
        col.prop(settings, "show_detailed_metrics")
        
        layout.separator()
        
        # Performance data display
        self._draw_performance_data(layout, context, settings)
    
    def _draw_performance_data(self, layout, context, settings):
        """Draw performance monitoring data"""
        
        # Provider performance
        if settings.monitor_providers:
            self._draw_provider_performance(layout)
        
        # Agent performance
        if settings.monitor_agents:
            self._draw_agent_performance(layout, settings)
        
        # Vision performance
        if settings.monitor_vision:
            self._draw_vision_performance(layout, settings)
        
        # Memory usage
        if settings.monitor_memory:
            self._draw_memory_usage(layout)
    
    def _draw_provider_performance(self, layout):
        """Draw provider performance metrics"""
        
        box = layout.box()
        box.label(text="Provider Performance", icon='NETWORK_DRIVE')
        
        try:
            provider_manager = get_provider_manager()
            status_summary = provider_manager.get_provider_status_summary()
            
            # Summary stats
            total_providers = status_summary.get("total_providers", 0)
            active_providers = status_summary.get("active_providers", 0)
            
            row = box.row()
            row.label(text=f"Total: {total_providers}")
            row.label(text=f"Active: {active_providers}")
            
            # Individual provider status
            providers = status_summary.get("providers", {})
            for provider_name, provider_info in providers.items():
                row = box.row()
                
                # Status icon
                test_status = provider_info.get("test_status", "unknown")
                if test_status == "success":
                    status_icon = 'CHECKMARK'
                elif test_status == "failed":
                    status_icon = 'X'
                else:
                    status_icon = 'QUESTION'
                
                row.label(text=provider_info.get("display_name", provider_name), icon=status_icon)
                row.label(text=f"Models: {provider_info.get('model_count', 0)}")
                
        except Exception as e:
            box.label(text=f"Error: {str(e)}", icon='ERROR')
    
    def _draw_agent_performance(self, layout, settings):
        """Draw agent performance metrics"""
        
        box = layout.box()
        box.label(text="Agent Performance", icon='COMMUNITY')
        
        try:
            agent_orchestrator = get_agent_orchestrator()
            performance_summary = agent_orchestrator.get_agent_performance_summary()
            
            agent_performance = performance_summary.get("agent_performance", {})
            
            if agent_performance:
                for agent_type_str, metrics in agent_performance.items():
                    row = box.row()
                    
                    # Agent name
                    agent_name = agent_type_str.replace('_', ' ').title()
                    row.label(text=agent_name)
                    
                    # Success rate
                    success_rate = metrics.get("success_rate", 0) * 100
                    row.label(text=f"{success_rate:.1f}%")
                    
                    if settings.show_detailed_metrics:
                        # Detailed metrics
                        detail_box = box.box()
                        detail_box.scale_y = 0.8
                        detail_box.label(text=f"Avg Response Time: {metrics.get('avg_response_time', 0):.2f}s")
                        detail_box.label(text=f"Total Requests: {metrics.get('total_requests', 0)}")
            else:
                box.label(text="No agent performance data available")
                
        except Exception as e:
            box.label(text=f"Error: {str(e)}", icon='ERROR')
    
    def _draw_vision_performance(self, layout, settings):
        """Draw vision system performance metrics"""
        
        box = layout.box()
        box.label(text="Vision Performance", icon='CAMERA_DATA')
        
        try:
            multi_modal_vision = get_multi_modal_vision()
            vision_summary = multi_modal_vision.get_vision_performance_summary()
            
            if vision_summary and not vision_summary.get('error'):
                # Cache statistics
                depth_cache = vision_summary.get("depth_analyzer", {}).get("cache_size", 0)
                spatial_cache = vision_summary.get("spatial_reasoner", {}).get("cache_size", 0)
                temporal_history = vision_summary.get("temporal_tracker", {}).get("state_history_size", 0)
                pipeline_cache = vision_summary.get("immersive_pipeline", {}).get("cache_size", 0)
                
                row = box.row()
                row.label(text=f"Depth Cache: {depth_cache}")
                row.label(text=f"Spatial Cache: {spatial_cache}")
                
                row = box.row()
                row.label(text=f"Temporal History: {temporal_history}")
                row.label(text=f"Pipeline Cache: {pipeline_cache}")
                
                if settings.show_detailed_metrics:
                    # Detailed vision metrics
                    detail_box = box.box()
                    detail_box.scale_y = 0.8
                    
                    temporal_tracker = vision_summary.get("temporal_tracker", {})
                    detail_box.label(text=f"Position Threshold: {temporal_tracker.get('position_threshold', 0)}")
                    detail_box.label(text=f"Rotation Threshold: {temporal_tracker.get('rotation_threshold', 0)}")
            else:
                error_msg = vision_summary.get('error', 'No vision performance data available')
                box.label(text=error_msg)
                
        except Exception as e:
            box.label(text=f"Error: {str(e)}", icon='ERROR')
    
    def _draw_memory_usage(self, layout):
        """Draw memory usage metrics"""
        
        box = layout.box()
        box.label(text="Memory Usage", icon='MEMORY')
        
        try:
            import psutil
            
            # System memory
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used = memory.used / (1024**3)  # GB
            memory_total = memory.total / (1024**3)  # GB
            
            row = box.row()
            row.label(text=f"System Memory: {memory_percent:.1f}%")
            row.label(text=f"({memory_used:.1f}/{memory_total:.1f} GB)")
            
            # Process memory (Blender)
            process = psutil.Process()
            process_memory = process.memory_info().rss / (1024**2)  # MB
            
            box.label(text=f"Blender Process: {process_memory:.1f} MB")
            
        except ImportError:
            box.label(text="psutil not available for memory monitoring")
        except Exception as e:
            box.label(text=f"Error: {str(e)}", icon='ERROR')

def register():
    """Register UI classes"""
    bpy.utils.register_class(BLENDPRO_PG_PerformanceSettings)
    bpy.utils.register_class(BLENDPRO_OT_RefreshPerformance)
    bpy.utils.register_class(BLENDPRO_OT_ClearPerformanceData)
    bpy.utils.register_class(BLENDPRO_OT_ExportPerformanceReport)
    bpy.utils.register_class(BLENDPRO_PT_PerformanceMonitor)

    # Register property groups
    bpy.types.Scene.blendpro_performance_settings = bpy.props.PointerProperty(type=BLENDPRO_PG_PerformanceSettings)
    bpy.types.Scene.blendpro_performance_last_refresh = bpy.props.StringProperty(name="Last Refresh", default="")

def unregister():
    """Unregister UI classes"""
    bpy.utils.unregister_class(BLENDPRO_PT_PerformanceMonitor)
    bpy.utils.unregister_class(BLENDPRO_OT_ExportPerformanceReport)
    bpy.utils.unregister_class(BLENDPRO_OT_ClearPerformanceData)
    bpy.utils.unregister_class(BLENDPRO_OT_RefreshPerformance)
    bpy.utils.unregister_class(BLENDPRO_PG_PerformanceSettings)

    # Unregister property groups
    del bpy.types.Scene.blendpro_performance_settings
    del bpy.types.Scene.blendpro_performance_last_refresh
