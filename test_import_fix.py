#!/usr/bin/env python3
"""
Quick test to verify the anthropic import fix
"""

import sys
import traceback

def test_dynamic_api_client_import():
    """Test that dynamic_api_client can be imported without errors"""
    print("Testing dynamic_api_client import...")
    
    try:
        # Add the BlendPro directory to path if needed
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Try to import the problematic module
        from utils.dynamic_api_client import DynamicAPIClient, get_dynamic_api_client
        print("✅ SUCCESS: dynamic_api_client imported successfully!")
        
        # Try to create an instance
        client = get_dynamic_api_client()
        print("✅ SUCCESS: DynamicAPIClient instance created successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        print("Full traceback:")
        traceback.print_exc()
        return False

def test_provider_manager_import():
    """Test that provider_manager can be imported"""
    print("\nTesting provider_manager import...")
    
    try:
        from utils.provider_manager import Provider<PERSON>anager, get_provider_manager
        print("✅ SUCCESS: provider_manager imported successfully!")
        
        # Try to create an instance
        manager = get_provider_manager()
        print("✅ SUCCESS: ProviderManager instance created successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        print("Full traceback:")
        traceback.print_exc()
        return False

def test_agent_orchestrator_import():
    """Test that agent_orchestrator can be imported"""
    print("\nTesting agent_orchestrator import...")
    
    try:
        from core.agent_orchestrator import AgentOrchestrator, get_agent_orchestrator
        print("✅ SUCCESS: agent_orchestrator imported successfully!")
        
        # Try to create an instance
        orchestrator = get_agent_orchestrator()
        print("✅ SUCCESS: AgentOrchestrator instance created successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        print("Full traceback:")
        traceback.print_exc()
        return False

def test_ui_modules_import():
    """Test that new UI modules can be imported"""
    print("\nTesting UI modules import...")
    
    ui_modules = [
        "ui.provider_settings",
        "ui.agent_config_panel", 
        "ui.vision_dashboard",
        "ui.performance_monitor"
    ]
    
    success_count = 0
    
    for module_name in ui_modules:
        try:
            __import__(module_name)
            print(f"✅ SUCCESS: {module_name} imported successfully!")
            success_count += 1
        except Exception as e:
            print(f"❌ FAILED: {module_name} - {e}")
    
    print(f"\nUI Modules: {success_count}/{len(ui_modules)} imported successfully")
    return success_count == len(ui_modules)

def main():
    """Run all import tests"""
    print("=" * 60)
    print("BlendPro v2.1.0 Import Fix Test")
    print("=" * 60)
    
    tests = [
        ("Dynamic API Client", test_dynamic_api_client_import),
        ("Provider Manager", test_provider_manager_import),
        ("Agent Orchestrator", test_agent_orchestrator_import),
        ("UI Modules", test_ui_modules_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Import fix successful!")
    else:
        print("❌ Some tests failed. Check errors above.")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    main()
