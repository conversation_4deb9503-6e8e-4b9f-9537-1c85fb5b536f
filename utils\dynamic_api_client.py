"""
Dynamic API Client for BlendPro v2.1.0
Multi-provider API client with dynamic provider switching and unified interface
"""

import asyncio
import time
import threading
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime

from ..config.providers import AIProvider, ProviderType
from ..config.agent_configs import AgentType
from ..utils.provider_manager import get_provider_manager
from ..utils.logger import get_logger
from ..utils.api_client import APIResponse, APIError

# Import provider-specific clients
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
    del anthropic  # Remove from namespace to avoid conflicts
except ImportError:
    ANTHROPIC_AVAILABLE = False

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False

@dataclass
class ProviderRequest:
    """Request specific to a provider"""
    provider_name: str
    model_name: str
    messages: List[Dict[str, Any]]
    temperature: float = 0.7
    max_tokens: int = 1500
    top_p: float = 1.0
    timeout: float = 30.0
    use_vision: bool = False
    stream: bool = False

@dataclass
class ProviderResponse:
    """Response from a provider"""
    content: str
    model: str
    provider_name: str
    usage: Dict[str, int]
    finish_reason: str
    response_time: float
    cost_estimate: float = 0.0
    error: Optional[str] = None

class DynamicAPIClient:
    """Multi-provider API client with dynamic switching"""
    
    def __init__(self):
        self.logger = get_logger("BlendPro.DynamicAPI")
        self.provider_manager = get_provider_manager()
        self._clients: Dict[str, Any] = {}
        self._rate_limiters: Dict[str, threading.Semaphore] = {}
        self._request_counts: Dict[str, int] = {}
        self._last_reset: Dict[str, float] = {}
        
        # Initialize rate limiters for all providers
        self._initialize_rate_limiters()
    
    def _initialize_rate_limiters(self):
        """Initialize rate limiters for all providers"""
        for provider_name, provider in self.provider_manager.get_all_providers().items():
            self._rate_limiters[provider_name] = threading.Semaphore(
                provider.rate_limits.concurrent_requests
            )
            self._request_counts[provider_name] = 0
            self._last_reset[provider_name] = time.time()
    
    def _get_client(self, provider: AIProvider) -> Any:
        """Get or create client for provider"""
        if provider.name in self._clients:
            return self._clients[provider.name]
        
        client = None
        
        if provider.provider_type == ProviderType.OPENAI:
            if not OPENAI_AVAILABLE:
                raise APIError("OpenAI library not available")
            
            client = OpenAI(
                api_key=provider.api_key,
                base_url=provider.api_endpoint,
                timeout=provider.timeout,
                max_retries=provider.max_retries
            )
        
        elif provider.provider_type == ProviderType.ANTHROPIC:
            if not ANTHROPIC_AVAILABLE:
                raise APIError("Anthropic library not available")

            # Import anthropic here to avoid import errors when not available
            import anthropic
            client = anthropic.Anthropic(
                api_key=provider.api_key,
                base_url=provider.api_endpoint,
                timeout=provider.timeout,
                max_retries=provider.max_retries
            )
        
        elif provider.provider_type == ProviderType.OPENROUTER:
            if not OPENAI_AVAILABLE:
                raise APIError("OpenAI library not available for OpenRouter")
            
            client = OpenAI(
                api_key=provider.api_key,
                base_url=provider.api_endpoint,
                timeout=provider.timeout,
                max_retries=provider.max_retries
            )
        
        elif provider.provider_type == ProviderType.CUSTOM:
            if not HTTPX_AVAILABLE:
                raise APIError("httpx library not available for custom providers")
            
            # Use httpx for custom providers
            client = httpx.Client(
                base_url=provider.api_endpoint,
                timeout=provider.timeout,
                headers=provider.custom_headers
            )
        
        else:
            raise APIError(f"Unsupported provider type: {provider.provider_type}")
        
        self._clients[provider.name] = client
        return client
    
    def _check_rate_limit(self, provider: AIProvider) -> bool:
        """Check if request is within rate limits"""
        current_time = time.time()
        provider_name = provider.name
        
        # Reset counter if a minute has passed
        if current_time - self._last_reset[provider_name] >= 60:
            self._request_counts[provider_name] = 0
            self._last_reset[provider_name] = current_time
        
        # Check if within rate limit
        if self._request_counts[provider_name] >= provider.rate_limits.requests_per_minute:
            return False
        
        return True
    
    def _estimate_cost(self, provider: AIProvider, usage: Dict[str, int], use_vision: bool = False) -> float:
        """Estimate cost of request"""
        input_tokens = usage.get("prompt_tokens", 0)
        output_tokens = usage.get("completion_tokens", 0)
        
        cost = (
            (input_tokens / 1000) * provider.cost_config.input_cost_per_1k_tokens +
            (output_tokens / 1000) * provider.cost_config.output_cost_per_1k_tokens
        )
        
        if use_vision and provider.cost_config.image_cost_per_request > 0:
            cost += provider.cost_config.image_cost_per_request
        
        return cost
    
    def make_request_with_provider(self, request: ProviderRequest) -> ProviderResponse:
        """Make request with specific provider"""
        provider = self.provider_manager.get_provider(request.provider_name)
        if not provider:
            return ProviderResponse(
                content="",
                model=request.model_name,
                provider_name=request.provider_name,
                usage={},
                finish_reason="error",
                response_time=0.0,
                error=f"Provider '{request.provider_name}' not found"
            )
        
        if not provider.is_active:
            return ProviderResponse(
                content="",
                model=request.model_name,
                provider_name=request.provider_name,
                usage={},
                finish_reason="error",
                response_time=0.0,
                error=f"Provider '{request.provider_name}' is not active"
            )
        
        # Check rate limits
        if not self._check_rate_limit(provider):
            return ProviderResponse(
                content="",
                model=request.model_name,
                provider_name=request.provider_name,
                usage={},
                finish_reason="error",
                response_time=0.0,
                error="Rate limit exceeded"
            )
        
        # Use rate limiter
        with self._rate_limiters[provider.name]:
            try:
                start_time = time.time()
                
                # Get client
                client = self._get_client(provider)
                
                # Make request based on provider type
                if provider.provider_type in [ProviderType.OPENAI, ProviderType.OPENROUTER]:
                    response = self._make_openai_request(client, request)
                elif provider.provider_type == ProviderType.ANTHROPIC:
                    response = self._make_anthropic_request(client, request)
                elif provider.provider_type == ProviderType.CUSTOM:
                    response = self._make_custom_request(client, request)
                else:
                    raise APIError(f"Unsupported provider type: {provider.provider_type}")
                
                response_time = time.time() - start_time
                
                # Update request count
                self._request_counts[provider.name] += 1
                
                # Estimate cost
                cost_estimate = self._estimate_cost(provider, response.usage, request.use_vision)
                
                return ProviderResponse(
                    content=response.content,
                    model=response.model,
                    provider_name=request.provider_name,
                    usage=response.usage,
                    finish_reason=response.finish_reason,
                    response_time=response_time,
                    cost_estimate=cost_estimate
                )
                
            except Exception as e:
                self.logger.error(f"Request failed for provider {request.provider_name}: {e}")
                return ProviderResponse(
                    content="",
                    model=request.model_name,
                    provider_name=request.provider_name,
                    usage={},
                    finish_reason="error",
                    response_time=time.time() - start_time if 'start_time' in locals() else 0.0,
                    error=str(e)
                )

    def _make_openai_request(self, client: OpenAI, request: ProviderRequest) -> APIResponse:
        """Make request to OpenAI-compatible API"""
        try:
            response = client.chat.completions.create(
                model=request.model_name,
                messages=request.messages,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                top_p=request.top_p,
                timeout=request.timeout,
                stream=request.stream
            )

            return APIResponse(
                content=response.choices[0].message.content,
                model=response.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                finish_reason=response.choices[0].finish_reason
            )

        except Exception as e:
            raise APIError(f"OpenAI API request failed: {str(e)}")

    def _make_anthropic_request(self, client: Any, request: ProviderRequest) -> APIResponse:
        """Make request to Anthropic API"""
        try:
            # Convert OpenAI format to Anthropic format
            system_message = ""
            messages = []

            for msg in request.messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    messages.append(msg)

            response = client.messages.create(
                model=request.model_name,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                system=system_message,
                messages=messages
            )

            return APIResponse(
                content=response.content[0].text,
                model=request.model_name,
                usage={
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                },
                finish_reason=response.stop_reason or "stop"
            )

        except Exception as e:
            raise APIError(f"Anthropic API request failed: {str(e)}")

    def _make_custom_request(self, client: httpx.Client, request: ProviderRequest) -> APIResponse:
        """Make request to custom API endpoint"""
        try:
            # Prepare request data in OpenAI format
            data = {
                "model": request.model_name,
                "messages": request.messages,
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
                "top_p": request.top_p,
                "stream": request.stream
            }

            response = client.post("/chat/completions", json=data, timeout=request.timeout)
            response.raise_for_status()

            result = response.json()

            return APIResponse(
                content=result["choices"][0]["message"]["content"],
                model=result["model"],
                usage=result.get("usage", {
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0
                }),
                finish_reason=result["choices"][0].get("finish_reason", "stop")
            )

        except Exception as e:
            raise APIError(f"Custom API request failed: {str(e)}")

    def make_request_with_agent(self, agent_type: AgentType, messages: List[Dict[str, Any]],
                               use_vision: bool = False, **kwargs) -> ProviderResponse:
        """Make request using agent configuration"""
        agent_config = self.provider_manager.get_agent_config(agent_type)
        if not agent_config:
            return ProviderResponse(
                content="",
                model="",
                provider_name="",
                usage={},
                finish_reason="error",
                response_time=0.0,
                error=f"Agent configuration not found for {agent_type.value}"
            )

        request = ProviderRequest(
            provider_name=agent_config.provider_name,
            model_name=agent_config.model_name,
            messages=messages,
            temperature=agent_config.temperature,
            max_tokens=agent_config.max_tokens,
            top_p=agent_config.top_p,
            use_vision=use_vision,
            **kwargs
        )

        # Try primary provider
        response = self.make_request_with_provider(request)

        # Try fallback if primary failed and fallback is configured
        if response.error and agent_config.fallback_provider:
            self.logger.warning(f"Primary provider failed, trying fallback: {agent_config.fallback_provider}")

            fallback_request = ProviderRequest(
                provider_name=agent_config.fallback_provider,
                model_name=agent_config.fallback_model or agent_config.model_name,
                messages=messages,
                temperature=agent_config.temperature,
                max_tokens=agent_config.max_tokens,
                top_p=agent_config.top_p,
                use_vision=use_vision,
                **kwargs
            )

            response = self.make_request_with_provider(fallback_request)

        return response

    def test_provider_connection(self, provider_name: str) -> Dict[str, Any]:
        """Test connection to a specific provider"""
        provider = self.provider_manager.get_provider(provider_name)
        if not provider:
            return {"success": False, "error": f"Provider '{provider_name}' not found"}

        test_request = ProviderRequest(
            provider_name=provider_name,
            model_name=provider.default_model or (provider.supported_models[0] if provider.supported_models else ""),
            messages=[{"role": "user", "content": "Hello, please respond with 'Connection successful!'"}],
            max_tokens=50,
            timeout=10
        )

        response = self.make_request_with_provider(test_request)

        if response.error:
            # Update provider test status
            provider.test_status = "failed"
            provider.last_tested = datetime.now().isoformat()
            return {"success": False, "error": response.error}

        # Update provider test status
        provider.test_status = "success"
        provider.last_tested = datetime.now().isoformat()

        return {
            "success": True,
            "content": response.content,
            "model": response.model,
            "response_time": response.response_time,
            "cost_estimate": response.cost_estimate
        }

# Global instance
_dynamic_api_client = None

def get_dynamic_api_client() -> DynamicAPIClient:
    """Get global dynamic API client instance"""
    global _dynamic_api_client
    if _dynamic_api_client is None:
        _dynamic_api_client = DynamicAPIClient()
    return _dynamic_api_client
